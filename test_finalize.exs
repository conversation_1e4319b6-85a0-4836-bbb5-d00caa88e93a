#!/usr/bin/env elixir

# Test script to verify finalize functionality

defmodule TestFinalize do
  use Drops.Operations

  operation :command do
    schema do
      %{
        required(:name) => string(:filled?)
      }
    end

    @impl true
    def execute(%{params: params}) do
      {:ok, %{user: params.name, id: :rand.uniform(1000)}}
    end
  end
end

# Test basic operation
IO.puts("Testing basic operation...")
result = TestFinalize.call(%{name: "<PERSON> Doe"})
IO.inspect(result, label: "Result")

case result do
  {:ok, %{user: "<PERSON> Doe", id: id}} when is_integer(id) ->
    IO.puts("✓ Basic operation returns finalized result correctly")
  other ->
    IO.puts("✗ Basic operation failed: #{inspect(other)}")
end

# Test error case
IO.puts("\nTesting error case...")
error_result = TestFinalize.call(%{name: ""})
IO.inspect(error_result, label: "Error Result")

case error_result do
  {:error, _error} ->
    IO.puts("✓ Error case returns finalized error correctly")
  other ->
    IO.puts("✗ Error case failed: #{inspect(other)}")
end

# Test composition
defmodule TestComposition do
  use Drops.Operations

  operation :command do
    @impl true
    def execute(previous_result, %{params: params}) do
      {:ok, Map.merge(previous_result, params)}
    end

    @impl true
    def execute(%{params: params}) do
      {:ok, params}
    end
  end
end

IO.puts("\nTesting composition...")
composition_result = TestFinalize.call(%{name: "Jane"}) |> TestComposition.call(%{age: 30})
IO.inspect(composition_result, label: "Composition Result")

case composition_result do
  {:ok, %{user: "Jane", id: _id, age: 30}} ->
    IO.puts("✓ Composition works correctly with finalized results")
  other ->
    IO.puts("✗ Composition failed: #{inspect(other)}")
end

IO.puts("\nAll tests completed!")
