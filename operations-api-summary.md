# Operations API Summary

The Operations API provides a framework for defining command and query operations with built-in input validation, preparation, and execution pipelines.

## Key Features

- **Schema validation** using Drops contracts
- **Pipeline processing** with conform → prepare → validate → execute steps
- **Operation composition** for chaining multiple operations
- **Extensible architecture** with auto-discovery and manual extension configuration
- **Base module pattern** for sharing common functionality across operations

## Basic Usage

### Simple Operation

```elixir
defmodule CreateUser do
  use Drops.Operations, type: :command

  schema do
    %{
      required(:name) => string(:filled?),
      required(:email) => string()
    }
  end

  @impl true
  def execute(%{params: params}) do
    # Your business logic here
    {:ok, %{id: 123, name: params.name, email: params.email}}
  end
end

# Usage
{:ok, %{result: user}} = CreateUser.call(%{name: "<PERSON>", email: "<EMAIL>"})
```

### Operation Composition

```elixir
# Chain operations together
result = CreateUser.call(%{name: "<PERSON>", email: "<EMAIL>"})
         |> UpdateUser.call(%{name: "<PERSON>"})

# Returns: {:ok, %Drops.Operations.Success{result: updated_user}}
```

## Advanced Features

- **prepare/1 callback** for transforming params before validation
- **Base modules** for sharing functions across operations
- **Extensions** for adding functionality like Ecto integration
- **Type safety** with Success/Failure structs for operation results
